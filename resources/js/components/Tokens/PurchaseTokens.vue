<template>
    <div class="bg-strava-surface min-h-screen">
        <h1 class="text-3xl font-bold text-strava-orange mb-6">Purchase Tokens</h1>

        <div class="bg-white p-6 rounded-lg shadow-md max-w-2xl mx-auto">
            <div v-if="user" class="mb-6">
                <p class="text-strava-grayMedium mb-2">Current token balance:</p>
                <p class="text-3xl font-bold text-strava-gray">{{ user.tokens }} <span class="text-xl">tokens</span></p>
            </div>

            <div v-if="message" class="p-4 mb-6" :class="messageClass">
                <p>{{ message }}</p>
            </div>

            <h2 class="text-xl font-bold text-strava-gray mb-6">Select a package:</h2>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                <div
                    v-for="(pkg, index) in packages"
                    :key="index"
                    class="border rounded-lg p-4 text-center hover:shadow-lg transition cursor-pointer"
                    :class="{ 'border-strava-orange': selectedPackage === index, 'border-gray-200': selectedPackage !== index }"
                    @click="selectPackage(index)"
                >
                    <h3 class="text-lg font-bold text-strava-gray mb-2">{{ pkg.name }}</h3>
                    <p class="text-2xl font-bold text-strava-orange mb-1">${{ pkg.price }}</p>
                    <p class="text-strava-grayLight mb-4">{{ pkg.tokens }} tokens</p>
                    <p class="text-xs text-strava-grayLight">{{ pkg.description }}</p>
                </div>
            </div>

            <!-- PayOS Payment Section -->
            <div v-if="selectedPackage !== null && !paymentCompleted" class="mb-6">
                <h3 class="text-lg font-bold text-strava-gray mb-2">Payment</h3>
                <div class="border border-gray-200 rounded-lg p-4">
                    <p class="text-strava-grayLight mb-4">Secure payment via PayOS</p>

                    <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-semibold">{{ packages[selectedPackage].name }} Package</span>
                            <span class="font-bold text-lg">${{ packages[selectedPackage].price }}</span>
                        </div>
                        <p class="text-sm text-gray-600">{{ packages[selectedPackage].tokens }} tokens • {{ packages[selectedPackage].description }}</p>
                    </div>

                    <div class="flex justify-center mb-4">
                        <button
                            @click="handlePayment"
                            class="bg-strava-orange text-white py-3 px-8 rounded hover:bg-strava-orangeLight text-lg font-semibold"
                            :disabled="loading"
                        >
                            {{ loading ? 'Creating Payment Link...' : 'Pay with PayOS' }}
                        </button>
                    </div>

                    <p class="text-xs text-gray-500 text-center">
                        You will be redirected to PayOS secure payment page
                    </p>
                </div>
            </div>

            <!-- Payment Success Message -->
            <div v-if="paymentCompleted" class="mb-6">
                <div class="text-center p-6 bg-green-50 border border-green-200 rounded-lg">
                    <div class="text-green-600 text-4xl mb-4">✓</div>
                    <h3 class="text-lg font-bold text-green-800 mb-2">Payment Successful!</h3>
                    <p class="text-green-700 mb-4">Your tokens have been added to your account.</p>
                    <button
                        @click="resetPayment"
                        class="bg-strava-orange text-white py-2 px-6 rounded hover:bg-strava-orangeLight"
                    >
                        Purchase More Tokens
                    </button>
                </div>
            </div>

            <!-- Payment Cancelled Message -->
            <div v-if="paymentCancelled" class="mb-6">
                <div class="text-center p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="text-yellow-600 text-4xl mb-4">⚠</div>
                    <h3 class="text-lg font-bold text-yellow-800 mb-2">Payment Cancelled</h3>
                    <p class="text-yellow-700 mb-4">Your payment was cancelled. No charges were made.</p>
                    <button
                        @click="resetPayment"
                        class="bg-strava-orange text-white py-2 px-6 rounded hover:bg-strava-orangeLight"
                    >
                        Try Again
                    </button>
                </div>
            </div>

            <!-- Debug Section (temporary) -->
            <div class="mb-6 p-4 bg-gray-100 border rounded-lg">
                <h4 class="font-bold mb-2">Debug Info:</h4>
                <p class="text-sm">Current URL: {{ currentUrl }}</p>
                <p class="text-sm">Payment Completed: {{ paymentCompleted }}</p>
                <p class="text-sm">Payment Cancelled: {{ paymentCancelled }}</p>
                <button
                    @click="manualCheckPaymentStatus"
                    class="mt-2 bg-blue-500 text-white px-4 py-2 rounded text-sm"
                >
                    Check Payment Status
                </button>
            </div>

            <!-- Default Purchase Button (when no package selected) -->
            <div v-if="selectedPackage === null && !paymentCompleted && !paymentCancelled" class="flex justify-end">
                <p class="text-strava-grayLight">Please select a package to continue</p>
            </div>
        </div>
    </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';

export default {
    name: 'PurchaseTokens',
    setup() {
        const router = useRouter();
        const route = useRoute();
        const user = ref(null);
        const selectedPackage = ref(null);
        const message = ref('');
        const messageClass = ref('');
        const loading = ref(false);

        // Payment state
        const paymentCompleted = ref(false);
        const paymentCancelled = ref(false);
        const currentUrl = ref(window.location.href);

        const packages = [
            {
                name: 'Basic',
                tokens: 5,
                price: '4.99',
                description: 'Perfect for occasional exports',
                type: 'basic'
            },
            {
                name: 'Standard',
                tokens: 15,
                price: '9.99',
                description: 'Most popular option',
                type: 'standard'
            },
            {
                name: 'Premium',
                tokens: 30,
                price: '14.99',
                description: 'Best value for active users',
                type: 'premium'
            }
        ];

        const fetchUserData = async () => {
            try {
                const response = await axios.get('/api/user');
                user.value = response.data;
            } catch (error) {
                console.error('Error fetching user data:', error);
                // Redirect to login if not authenticated
                if (error.response && error.response.status === 401) {
                    router.push({ name: 'login' });
                }
            }
        };

        // Check URL parameters for payment status on component mount
        const checkPaymentStatus = () => {
            const urlParams = new URLSearchParams(window.location.search);

            console.log('Checking payment status...', {
                url: window.location.href,
                search: window.location.search,
                success: urlParams.get('success'),
                canceled: urlParams.get('canceled'),
                status: urlParams.get('status'),
                orderCode: urlParams.get('orderCode')
            });

            if (urlParams.get('success') === 'true') {
                console.log('Payment success detected');
                paymentCompleted.value = true;
                message.value = 'Payment completed successfully! Your tokens have been added to your account.';
                messageClass.value = 'bg-green-100 text-green-700 border-l-4 border-green-500';

                // Update user data to reflect new token balance
                fetchUserData();

                // Clean up URL parameters after a short delay to ensure the message is shown
                setTimeout(() => {
                    router.replace({ name: 'purchase-tokens' });
                }, 100);
            } else if (urlParams.get('canceled') === 'true') {
                console.log('Payment cancellation detected');
                paymentCancelled.value = true;
                message.value = 'Payment was cancelled. No charges were made.';
                messageClass.value = 'bg-yellow-100 text-yellow-700 border-l-4 border-yellow-500';

                // Clean up URL parameters after a short delay to ensure the message is shown
                setTimeout(() => {
                    router.replace({ name: 'purchase-tokens' });
                }, 100);
            }
        };

        onMounted(async () => {
            console.log('PurchaseTokens component mounted');
            await fetchUserData();
            checkPaymentStatus();
        });

        // Watch for route changes to catch payment status updates
        watch(() => route.query, (newQuery) => {
            console.log('Route query changed:', newQuery);
            if (newQuery.success || newQuery.canceled) {
                checkPaymentStatus();
            }
        }, { immediate: true });

        const selectPackage = (index) => {
            selectedPackage.value = index;
            // Reset payment state when selecting a new package
            resetPaymentState();
        };

        const handlePayment = async () => {
            if (selectedPackage.value === null) return;

            loading.value = true;
            message.value = '';

            try {
                const pkg = packages[selectedPackage.value];
                const response = await axios.post('/api/payos/create-payment', {
                    package_type: pkg.type
                });

                if (response.data.success) {
                    const { checkoutUrl } = response.data.data;

                    // Redirect to PayOS hosted checkout page
                    window.location.href = checkoutUrl;
                } else {
                    throw new Error(response.data.message || 'Failed to create payment link');
                }

            } catch (error) {
                console.error('Payment link creation error:', error);
                message.value = error.response?.data?.message || 'Failed to create payment link. Please try again.';
                messageClass.value = 'bg-red-100 text-red-700 border-l-4 border-red-500';
                loading.value = false;
            }
        };

        const resetPayment = () => {
            resetPaymentState();
            selectedPackage.value = null;
            message.value = '';
        };

        const resetPaymentState = () => {
            paymentCompleted.value = false;
            paymentCancelled.value = false;
        };

        const manualCheckPaymentStatus = () => {
            currentUrl.value = window.location.href;
            checkPaymentStatus();
        };

        return {
            user,
            packages,
            selectedPackage,
            message,
            messageClass,
            loading,
            paymentCompleted,
            paymentCancelled,
            currentUrl,
            selectPackage,
            handlePayment,
            resetPayment,
            manualCheckPaymentStatus
        };
    }
}
</script>

<style>
/* Strava style customizations */
input:focus {
    outline: none;
    border-color: #FC4C02;
}
</style>
