<template>
    <div class="bg-strava-surface min-h-screen">
        <h1 class="text-3xl font-bold text-strava-orange mb-6">Purchase Tokens</h1>

        <div class="bg-white p-6 rounded-lg shadow-md max-w-2xl mx-auto">
            <div v-if="user" class="mb-6">
                <p class="text-strava-grayMedium mb-2">Current token balance:</p>
                <p class="text-3xl font-bold text-strava-gray">{{ user.tokens }} <span class="text-xl">tokens</span></p>
            </div>

            <div v-if="message" class="p-4 mb-6" :class="messageClass">
                <p>{{ message }}</p>
            </div>

            <h2 class="text-xl font-bold text-strava-gray mb-6">Select a package:</h2>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                <div
                    v-for="(pkg, index) in packages"
                    :key="index"
                    class="border rounded-lg p-4 text-center hover:shadow-lg transition cursor-pointer"
                    :class="{ 'border-strava-orange': selectedPackage === index, 'border-gray-200': selectedPackage !== index }"
                    @click="selectPackage(index)"
                >
                    <h3 class="text-lg font-bold text-strava-gray mb-2">{{ pkg.name }}</h3>
                    <p class="text-2xl font-bold text-strava-orange mb-1">${{ pkg.price }}</p>
                    <p class="text-strava-grayLight mb-4">{{ pkg.tokens }} tokens</p>
                    <p class="text-xs text-strava-grayLight">{{ pkg.description }}</p>
                </div>
            </div>

            <!-- PayOS Payment Section -->
            <div v-if="selectedPackage !== null && !paymentCompleted" class="mb-6">
                <h3 class="text-lg font-bold text-strava-gray mb-2">Payment</h3>
                <div class="border border-gray-200 rounded-lg p-4">
                    <p class="text-strava-grayLight mb-4">Secure payment via PayOS</p>

                    <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-semibold">{{ packages[selectedPackage].name }} Package</span>
                            <span class="font-bold text-lg">${{ packages[selectedPackage].price }}</span>
                        </div>
                        <p class="text-sm text-gray-600">{{ packages[selectedPackage].tokens }} tokens • {{ packages[selectedPackage].description }}</p>
                    </div>

                    <div class="flex justify-between mb-4">
                        <button
                            v-if="!paymentLinkCreated"
                            @click="createPaymentLink"
                            class="bg-strava-orange text-white py-2 px-6 rounded hover:bg-strava-orangeLight"
                            :disabled="loading"
                        >
                            {{ loading ? 'Creating Payment...' : 'Pay with PayOS' }}
                        </button>

                        <button
                            v-if="paymentLinkCreated"
                            @click="closePayment"
                            class="bg-gray-500 text-white py-2 px-6 rounded hover:bg-gray-600"
                        >
                            Close Payment
                        </button>
                    </div>

                    <!-- PayOS Embedded Payment Container -->
                    <div id="payos-embedded-container" style="height: 400px;" class="border rounded-lg"></div>
                </div>
            </div>

            <!-- Payment Success Message -->
            <div v-if="paymentCompleted" class="mb-6">
                <div class="text-center p-6 bg-green-50 border border-green-200 rounded-lg">
                    <div class="text-green-600 text-4xl mb-4">✓</div>
                    <h3 class="text-lg font-bold text-green-800 mb-2">Payment Successful!</h3>
                    <p class="text-green-700 mb-4">Your tokens have been added to your account.</p>
                    <button
                        @click="resetPayment"
                        class="bg-strava-orange text-white py-2 px-6 rounded hover:bg-strava-orangeLight"
                    >
                        Purchase More Tokens
                    </button>
                </div>
            </div>

            <!-- Default Purchase Button (when no package selected) -->
            <div v-if="selectedPackage === null" class="flex justify-end">
                <p class="text-strava-grayLight">Please select a package to continue</p>
            </div>
        </div>
    </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import { usePayOS } from '@payos/payos-checkout';

export default {
    name: 'PurchaseTokens',
    setup() {
        const router = useRouter();
        const user = ref(null);
        const selectedPackage = ref(null);
        const message = ref('');
        const messageClass = ref('');
        const loading = ref(false);

        // PayOS specific state
        const paymentLinkCreated = ref(false);
        const paymentCompleted = ref(false);
        const currentOrderCode = ref(null);
        const currentCheckoutUrl = ref(null);
        const payosInstance = ref(null);

        // PayOS configuration - using reactive object instead of ref
        const payOSConfig = reactive({
            RETURN_URL: window.location.href,
            ELEMENT_ID: "payos-embedded-container",
            CHECKOUT_URL: null,
            embedded: true,
            onSuccess: async (event) => {
                console.log('Payment successful:', event);

                // Update user data to reflect new token balance - not needed for now
                // await fetchUserData();

                paymentCompleted.value = true;
                paymentLinkCreated.value = false;

                message.value = 'Payment completed successfully! Your tokens have been added to your account.';
                messageClass.value = 'bg-green-100 text-green-700 border-l-4 border-green-500';
            },
            onCancel: (event) => {
                console.log('Payment cancelled:', event);
                message.value = 'Payment was cancelled.';
                messageClass.value = 'bg-yellow-100 text-yellow-700 border-l-4 border-yellow-500';
            },
            onError: (event) => {
                console.error('Payment error:', event);
                message.value = 'Payment failed. Please try again.';
                messageClass.value = 'bg-red-100 text-red-700 border-l-4 border-red-500';
            }
        });

        const packages = [
            {
                name: 'Basic',
                tokens: 5,
                price: '4.99',
                description: 'Perfect for occasional exports',
                type: 'basic'
            },
            {
                name: 'Standard',
                tokens: 15,
                price: '9.99',
                description: 'Most popular option',
                type: 'standard'
            },
            {
                name: 'Premium',
                tokens: 30,
                price: '14.99',
                description: 'Best value for active users',
                type: 'premium'
            }
        ];

        // Initialize PayOS when needed
        const initializePayOS = () => {
            if (!payosInstance.value && payOSConfig.CHECKOUT_URL) {
                console.log('Initializing PayOS with URL:', payOSConfig.CHECKOUT_URL);
                try {
                    const { open, exit } = usePayOS(payOSConfig);
                    payosInstance.value = { open, exit };
                    open();
                } catch (error) {
                    console.error('Error initializing PayOS:', error);
                    message.value = 'Failed to initialize payment form. Please try again.';
                    messageClass.value = 'bg-red-100 text-red-700 border-l-4 border-red-500';
                }
            }
        };

        // Watch for checkout URL changes and trigger payment form opening
        watch(() => payOSConfig.CHECKOUT_URL, (newUrl) => {
            if (newUrl) {
                console.log('Checkout URL changed:', newUrl);
                // Clean up existing instance first
                if (payosInstance.value) {
                    try {
                        payosInstance.value.exit();
                    } catch (error) {
                        console.log('No existing iframe to clean up');
                    }
                    payosInstance.value = null;
                }
                // Initialize new instance
                setTimeout(() => {
                    initializePayOS();
                }, 100); // Small delay to ensure DOM is ready
            }
        });

        onMounted(async () => {
            await fetchUserData();
        });

        onUnmounted(() => {
            // Clean up PayOS checkout if it exists
            if (payosInstance.value) {
                try {
                    payosInstance.value.exit();
                } catch (error) {
                    console.log('PayOS cleanup error:', error);
                }
                payosInstance.value = null;
            }
        });

        const fetchUserData = async () => {
            try {
                const response = await axios.get('/api/user');
                user.value = response.data;
            } catch (error) {
                console.error('Error fetching user data:', error);
                // Redirect to login if not authenticated
                if (error.response && error.response.status === 401) {
                    router.push({ name: 'login' });
                }
            }
        };

        const selectPackage = (index) => {
            selectedPackage.value = index;
            // Reset payment state when selecting a new package
            resetPaymentState();
        };

        const createPaymentLink = async () => {
            if (selectedPackage.value === null) return;

            loading.value = true;
            message.value = '';

            try {
                const pkg = packages[selectedPackage.value];
                const response = await axios.post('/api/payos/create-payment', {
                    package_type: pkg.type
                });

                if (response.data.success) {
                    const { checkoutUrl, orderCode } = response.data.data;
                    currentOrderCode.value = orderCode;
                    currentCheckoutUrl.value = checkoutUrl;

                    // Update PayOS config with new checkout URL - this will trigger the watcher
                    payOSConfig.CHECKOUT_URL = checkoutUrl;

                    paymentLinkCreated.value = true;
                    message.value = '';
                } else {
                    throw new Error(response.data.message || 'Failed to create payment link');
                }

            } catch (error) {
                console.error('Payment link creation error:', error);
                message.value = error.response?.data?.message || 'Failed to create payment link. Please try again.';
                messageClass.value = 'bg-red-100 text-red-700 border-l-4 border-red-500';
            } finally {
                loading.value = false;
            }
        };

        const closePayment = () => {
            if (payosInstance.value) {
                try {
                    payosInstance.value.exit();
                } catch (error) {
                    console.log('Error closing PayOS:', error);
                }
                payosInstance.value = null;
            }
            resetPaymentState();
        };

        const resetPayment = () => {
            resetPaymentState();
            selectedPackage.value = null;
            message.value = '';
        };

        const resetPaymentState = () => {
            paymentLinkCreated.value = false;
            paymentCompleted.value = false;
            currentOrderCode.value = null;
            currentCheckoutUrl.value = null;
            // Clean up PayOS instance
            if (payosInstance.value) {
                try {
                    payosInstance.value.exit();
                } catch (error) {
                    console.log('No iframe to clean up during reset');
                }
                payosInstance.value = null;
            }
            // Reset PayOS config
            payOSConfig.CHECKOUT_URL = null;
        };

        return {
            user,
            packages,
            selectedPackage,
            message,
            messageClass,
            loading,
            paymentLinkCreated,
            paymentCompleted,
            selectPackage,
            createPaymentLink,
            closePayment,
            resetPayment
        };
    }
}
</script>

<style>
/* Strava style customizations */
input:focus {
    outline: none;
    border-color: #FC4C02;
}
</style>
